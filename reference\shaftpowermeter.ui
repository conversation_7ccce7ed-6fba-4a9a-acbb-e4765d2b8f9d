<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ShaftPowerMeter</class>
 <widget class="QWidget" name="ShaftPowerMeter">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Shaft Power Meter</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_main">
   <item>
    <widget class="QGroupBox" name="groupBox_settings">
     <property name="title">
      <string>Shaft Device Settings</string>
     </property>
     <property name="minimumSize">
      <size>
       <width>350</width>
       <height>0</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_settings">
      <item>
       <spacer name="verticalSpacer_settings">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_addDevice">
        <property name="text">
         <string>Add Device</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_data">
     <property name="title">
      <string>Shaft Device Data</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_data">
      <item>
       <widget class="QTabWidget" name="tabWidget_deviceData">
        <property name="currentIndex">
         <number>0</number>
        </property>
        <widget class="QWidget" name="tab_device1">
         <attribute name="title">
          <string>Device 1</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_device1">
          <item>
           <widget class="QTableWidget" name="tableWidget_device1">
            <property name="alternatingRowColors">
             <bool>true</bool>
            </property>
            <property name="sortingEnabled">
             <bool>false</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 