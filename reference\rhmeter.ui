<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RHMeter</class>
 <widget class="QWidget" name="RHMeter">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RH Meter</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_main">
   <item>
    <widget class="QGroupBox" name="groupBox_settings">
     <property name="minimumSize">
      <size>
       <width>350</width>
       <height>0</height>
      </size>
     </property>
     <property name="title">
      <string>RH Device Settings</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_settings">
      <item>
       <widget class="QPushButton" name="pushButton_addDevice">
        <property name="text">
         <string>Add Device</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_data">
     <property name="title">
      <string>RH Device Data</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_dataDisplay">
      <item>
       <widget class="QTabWidget" name="tabWidget_deviceData">
        <property name="currentIndex">
         <number>0</number>
        </property>
        <widget class="QWidget" name="tab_device1">
         <attribute name="title">
          <string>Device 1</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_tab1">
          <item>
           <widget class="QTableWidget" name="tableWidget_device1"/>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
